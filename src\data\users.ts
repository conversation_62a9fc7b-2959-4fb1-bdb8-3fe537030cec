import { User } from '../types/dashboard'

export const mockUsers: User[] = [
  {
    id: '1',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    workspaceName: 'TechCorp Solutions',
    subscriptionStatus: 'Active',
    planType: 'Enterprise',
    lastRenewalDate: '2024-01-15',
    accountCreatedDate: '2023-03-10',
    lastLoginDate: '2024-07-13',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing', 'user_management']
  },
  {
    id: '2',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    workspaceName: 'Creative Design Studio',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-06-20',
    accountCreatedDate: '2023-08-15',
    lastLoginDate: '2024-07-12',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '3',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    workspaceName: 'StartupInc',
    subscriptionStatus: 'Expired',
    planType: 'Basic',
    lastRenewalDate: '2024-05-01',
    accountCreatedDate: '2024-01-20',
    lastLoginDate: '2024-06-15',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '4',
    fullName: 'Emily Rodriguez',
    email: '<EMAIL>',
    workspaceName: 'Marketing Pro Agency',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-07-01',
    accountCreatedDate: '2023-11-05',
    lastLoginDate: '2024-07-14',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '5',
    fullName: 'David Wilson',
    email: '<EMAIL>',
    workspaceName: 'Wilson Consulting',
    subscriptionStatus: 'Inactive',
    planType: 'Basic',
    lastRenewalDate: '2024-03-15',
    accountCreatedDate: '2023-12-01',
    lastLoginDate: '2024-04-20',
    isDisabled: true,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '6',
    fullName: 'Lisa Thompson',
    email: '<EMAIL>',
    workspaceName: 'E-Commerce Solutions',
    subscriptionStatus: 'Active',
    planType: 'Enterprise',
    lastRenewalDate: '2024-02-28',
    accountCreatedDate: '2023-05-12',
    lastLoginDate: '2024-07-13',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing', 'user_management']
  },
  {
    id: '7',
    fullName: 'Robert Garcia',
    email: '<EMAIL>',
    workspaceName: 'Garcia & Associates',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-04-10',
    accountCreatedDate: '2023-09-20',
    lastLoginDate: '2024-07-11',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '8',
    fullName: 'Amanda Lee',
    email: '<EMAIL>',
    workspaceName: 'HealthTech Innovations',
    subscriptionStatus: 'Expired',
    planType: 'Premium',
    lastRenewalDate: '2024-04-30',
    accountCreatedDate: '2023-07-08',
    lastLoginDate: '2024-05-25',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '9',
    fullName: 'James Brown',
    email: '<EMAIL>',
    workspaceName: 'Brown Real Estate',
    subscriptionStatus: 'Active',
    planType: 'Basic',
    lastRenewalDate: '2024-06-15',
    accountCreatedDate: '2024-02-14',
    lastLoginDate: '2024-07-10',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '10',
    fullName: 'Jennifer Davis',
    email: '<EMAIL>',
    workspaceName: 'Davis Financial Services',
    subscriptionStatus: 'Active',
    planType: 'Enterprise',
    lastRenewalDate: '2024-05-20',
    accountCreatedDate: '2023-04-18',
    lastLoginDate: '2024-07-14',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing', 'user_management']
  },
  {
    id: '11',
    fullName: 'Kevin Martinez',
    email: '<EMAIL>',
    workspaceName: 'Martinez Architecture',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-03-25',
    accountCreatedDate: '2023-10-12',
    lastLoginDate: '2024-07-09',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '12',
    fullName: 'Rachel Green',
    email: '<EMAIL>',
    workspaceName: 'Green Fashion Brand',
    subscriptionStatus: 'Inactive',
    planType: 'Basic',
    lastRenewalDate: '2024-02-10',
    accountCreatedDate: '2023-12-20',
    lastLoginDate: '2024-03-15',
    isDisabled: true,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '13',
    fullName: 'Thomas Anderson',
    email: '<EMAIL>',
    workspaceName: 'CyberSec Solutions',
    subscriptionStatus: 'Active',
    planType: 'Enterprise',
    lastRenewalDate: '2024-07-05',
    accountCreatedDate: '2023-06-30',
    lastLoginDate: '2024-07-14',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing', 'user_management']
  },
  {
    id: '14',
    fullName: 'Maria Gonzalez',
    email: '<EMAIL>',
    workspaceName: 'Gonzalez Restaurant Group',
    subscriptionStatus: 'Expired',
    planType: 'Premium',
    lastRenewalDate: '2024-03-31',
    accountCreatedDate: '2023-08-25',
    lastLoginDate: '2024-04-10',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '15',
    fullName: 'Christopher Taylor',
    email: '<EMAIL>',
    workspaceName: 'Taylor Auto Repair',
    subscriptionStatus: 'Active',
    planType: 'Basic',
    lastRenewalDate: '2024-05-12',
    accountCreatedDate: '2024-01-08',
    lastLoginDate: '2024-07-08',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '16',
    fullName: 'Nicole White',
    email: '<EMAIL>',
    workspaceName: 'White Photography Studio',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-06-08',
    accountCreatedDate: '2023-09-15',
    lastLoginDate: '2024-07-12',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '17',
    fullName: 'Daniel Kim',
    email: '<EMAIL>',
    workspaceName: 'Kim Software Development',
    subscriptionStatus: 'Active',
    planType: 'Enterprise',
    lastRenewalDate: '2024-04-22',
    accountCreatedDate: '2023-02-28',
    lastLoginDate: '2024-07-13',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing', 'user_management']
  },
  {
    id: '18',
    fullName: 'Ashley Miller',
    email: '<EMAIL>',
    workspaceName: 'Miller Event Planning',
    subscriptionStatus: 'Inactive',
    planType: 'Basic',
    lastRenewalDate: '2024-01-20',
    accountCreatedDate: '2023-11-30',
    lastLoginDate: '2024-02-28',
    isDisabled: true,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '19',
    fullName: 'Ryan Clark',
    email: '<EMAIL>',
    workspaceName: 'Clark Fitness Club',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-07-02',
    accountCreatedDate: '2023-12-05',
    lastLoginDate: '2024-07-11',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '20',
    fullName: 'Stephanie Lewis',
    email: '<EMAIL>',
    workspaceName: 'Lewis Pet Care Services',
    subscriptionStatus: 'Expired',
    planType: 'Basic',
    lastRenewalDate: '2024-04-15',
    accountCreatedDate: '2023-10-22',
    lastLoginDate: '2024-05-01',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin']
  },
  {
    id: '21',
    fullName: 'Brandon Hall',
    email: '<EMAIL>',
    workspaceName: 'Hall Music Production',
    subscriptionStatus: 'Active',
    planType: 'Premium',
    lastRenewalDate: '2024-06-25',
    accountCreatedDate: '2023-07-14',
    lastLoginDate: '2024-07-10',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin', 'billing']
  },
  {
    id: '22',
    fullName: 'Melissa Young',
    email: '<EMAIL>',
    workspaceName: 'Young Beauty Salon',
    subscriptionStatus: 'Active',
    planType: 'Basic',
    lastRenewalDate: '2024-05-30',
    accountCreatedDate: '2024-03-01',
    lastLoginDate: '2024-07-09',
    isDisabled: false,
    role: 'Owner',
    permissions: ['admin']
  }
]
