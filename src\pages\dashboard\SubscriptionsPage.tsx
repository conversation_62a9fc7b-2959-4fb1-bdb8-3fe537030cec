import { useState, useMemo } from 'react'
import { CreditCard, Plus, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  DataTable, 
  SearchInput, 
  FilterDropdown, 
  FormDialog,
  StatusBadge 
} from '@/components/dashboard'
import { mockSubscriptions, mockRenewalLogs, mockUsers, mockPlans } from '@/data'
import { type Subscription, type RenewalLog, type TableColumn, type FilterOption, type PaginationInfo } from '@/types/dashboard'
import { toast } from 'sonner'

export function SubscriptionsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPlanTypes, setSelectedPlanTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [sortColumn, setSortColumn] = useState<keyof Subscription>('userName')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [showRenewalDialog, setShowRenewalDialog] = useState(false)
  const [loading, setLoading] = useState(false)

  // Renewal form state
  const [renewalForm, setRenewalForm] = useState({
    userId: '',
    startDate: '',
    endDate: '',
    planType: '',
    referenceNumber: ''
  })

  const itemsPerPage = 10

  // Filter options
  const planTypeOptions: FilterOption[] = [
    { value: 'Basic', label: 'Basic', count: mockSubscriptions.filter(s => s.planType === 'Basic').length },
    { value: 'Premium', label: 'Premium', count: mockSubscriptions.filter(s => s.planType === 'Premium').length },
    { value: 'Enterprise', label: 'Enterprise', count: mockSubscriptions.filter(s => s.planType === 'Enterprise').length }
  ]

  const statusOptions: FilterOption[] = [
    { value: 'Active', label: 'Active', count: mockSubscriptions.filter(s => s.status === 'Active').length },
    { value: 'Inactive', label: 'Inactive', count: mockSubscriptions.filter(s => s.status === 'Inactive').length },
    { value: 'Expired', label: 'Expired', count: mockSubscriptions.filter(s => s.status === 'Expired').length },
    { value: 'Cancelled', label: 'Cancelled', count: mockSubscriptions.filter(s => s.status === 'Cancelled').length }
  ]

  // Filtered and sorted data
  const filteredData = useMemo(() => {
    let filtered = mockSubscriptions.filter(subscription => {
      const matchesSearch = searchTerm === '' || 
        subscription.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subscription.workspaceName.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesPlanType = selectedPlanTypes.length === 0 || 
        selectedPlanTypes.includes(subscription.planType)
      
      const matchesStatus = selectedStatuses.length === 0 || 
        selectedStatuses.includes(subscription.status)

      return matchesSearch && matchesPlanType && matchesStatus
    })

    // Sort data
    filtered.sort((a, b) => {
      const aValue = a[sortColumn]
      const bValue = b[sortColumn]

      if (aValue == null && bValue == null) return 0
      if (aValue == null) return 1
      if (bValue == null) return -1

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [searchTerm, selectedPlanTypes, selectedStatuses, sortColumn, sortDirection])

  // Pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredData.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredData, currentPage])

  const pagination: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(filteredData.length / itemsPerPage),
    totalItems: filteredData.length,
    itemsPerPage
  }

  // Table columns
  const columns: TableColumn<Subscription>[] = [
    {
      key: 'userName',
      label: 'User Name',
      sortable: true
    },
    {
      key: 'workspaceName',
      label: 'Workspace',
      sortable: true
    },
    {
      key: 'planType',
      label: 'Plan Type',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'startDate',
      label: 'Start Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'endDate',
      label: 'End Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => <StatusBadge status={value} />
    },
    {
      key: 'nextBillingDate',
      label: 'Next Billing',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleManualRenewal = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success('Manual renewal completed successfully')
    setShowRenewalDialog(false)
    setRenewalForm({
      userId: '',
      startDate: '',
      endDate: '',
      planType: '',
      referenceNumber: ''
    })
  }

  // Renewal logs table columns
  const renewalColumns: TableColumn<RenewalLog>[] = [
    {
      key: 'timestamp',
      label: 'Date & Time',
      sortable: true,
      render: (value) => formatDateTime(value)
    },
    {
      key: 'userName',
      label: 'User',
      sortable: true
    },
    {
      key: 'workspaceName',
      label: 'Workspace',
      sortable: true
    },
    {
      key: 'planType',
      label: 'Plan',
      sortable: true
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value) => formatCurrency(value)
    },
    {
      key: 'adminUser',
      label: 'Admin',
      sortable: true
    },
    {
      key: 'referenceNumber',
      label: 'Reference',
      sortable: true
    }
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
          <p className="text-muted-foreground">
            Manage user subscriptions, renewals, and billing information
          </p>
        </div>
        <Button onClick={() => setShowRenewalDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Manual Renewal
        </Button>
      </div>

      {/* Subscriptions Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Active Subscriptions ({filteredData.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <SearchInput
              placeholder="Search by user name or workspace..."
              value={searchTerm}
              onChange={setSearchTerm}
              className="flex-1"
            />
            <FilterDropdown
              label="Plan Type"
              options={planTypeOptions}
              selectedValues={selectedPlanTypes}
              onSelectionChange={setSelectedPlanTypes}
              className="w-full sm:w-auto"
            />
            <FilterDropdown
              label="Status"
              options={statusOptions}
              selectedValues={selectedStatuses}
              onSelectionChange={setSelectedStatuses}
              className="w-full sm:w-auto"
            />
          </div>

          <DataTable
            data={paginatedData}
            columns={columns}
            loading={loading}
            pagination={pagination}
            onPageChange={setCurrentPage}
            onSort={(column, direction) => {
              setSortColumn(column)
              setSortDirection(direction)
            }}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            emptyState={{
              title: 'No subscriptions found',
              description: 'No subscriptions match your current search and filter criteria.',
              action: {
                label: 'Clear filters',
                onClick: () => {
                  setSearchTerm('')
                  setSelectedPlanTypes([])
                  setSelectedStatuses([])
                }
              }
            }}
          />
        </CardContent>
      </Card>

      {/* Renewal Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Manual Renewals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={mockRenewalLogs}
            columns={renewalColumns}
            emptyState={{
              title: 'No renewal logs',
              description: 'No manual renewals have been processed recently.'
            }}
          />
        </CardContent>
      </Card>

      {/* Manual Renewal Dialog */}
      <FormDialog
        open={showRenewalDialog}
        onOpenChange={setShowRenewalDialog}
        title="Manual Subscription Renewal"
        description="Process a manual subscription renewal for a user"
        onSubmit={handleManualRenewal}
        submitText="Process Renewal"
        loading={loading}
        size="lg"
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="user-select">Select User</Label>
            <Select
              value={renewalForm.userId}
              onValueChange={(value) => setRenewalForm(prev => ({ ...prev, userId: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a user..." />
              </SelectTrigger>
              <SelectContent>
                {mockUsers.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.fullName} - {user.workspaceName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={renewalForm.startDate}
                onChange={(e) => setRenewalForm(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-date">End Date</Label>
              <Input
                id="end-date"
                type="date"
                value={renewalForm.endDate}
                onChange={(e) => setRenewalForm(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="plan-select">Plan Type</Label>
            <Select
              value={renewalForm.planType}
              onValueChange={(value) => setRenewalForm(prev => ({ ...prev, planType: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select plan type..." />
              </SelectTrigger>
              <SelectContent>
                {mockPlans.filter(plan => plan.isActive).map((plan) => (
                  <SelectItem key={plan.id} value={plan.name}>
                    {plan.name} - {formatCurrency(plan.monthlyPrice)}/month
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reference-number">Reference Number</Label>
            <Input
              id="reference-number"
              placeholder="Enter reference number..."
              value={renewalForm.referenceNumber}
              onChange={(e) => setRenewalForm(prev => ({ ...prev, referenceNumber: e.target.value }))}
            />
          </div>
        </div>
      </FormDialog>
    </div>
  )
}
