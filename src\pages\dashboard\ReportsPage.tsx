import { useState } from 'react'
import { BarChart3, Download, FileText, Users, CreditCard, Shield, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FormDialog, StatusBadge } from '@/components/dashboard'
import { mockUsers, mockSubscriptions, mockAuditLogs } from '@/data'
import { ExportData } from '@/types/dashboard'
import { toast } from 'sonner'

export function ReportsPage() {
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [selectedExportType, setSelectedExportType] = useState<'users' | 'subscriptions' | 'audit-logs'>('users')
  const [exportFormat, setExportFormat] = useState<'csv' | 'xlsx' | 'json'>('csv')
  const [dateRange, setDateRange] = useState({ start: '', end: '' })
  const [loading, setLoading] = useState(false)

  // Sample data for previews
  const userPreviewData = mockUsers.slice(0, 5)
  const subscriptionPreviewData = mockSubscriptions.slice(0, 5)
  const auditLogPreviewData = mockAuditLogs.slice(0, 5)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleExport = async () => {
    setLoading(true)
    
    // Simulate export process
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Create mock download
    const exportData: ExportData = {
      type: selectedExportType,
      filename: `${selectedExportType}-export-${new Date().toISOString().split('T')[0]}.${exportFormat}`,
      data: selectedExportType === 'users' ? mockUsers : 
            selectedExportType === 'subscriptions' ? mockSubscriptions : 
            mockAuditLogs,
      columns: selectedExportType === 'users' ? 
        ['Full Name', 'Email', 'Workspace', 'Plan Type', 'Status', 'Created Date'] :
        selectedExportType === 'subscriptions' ?
        ['User Name', 'Workspace', 'Plan Type', 'Start Date', 'End Date', 'Status', 'Amount'] :
        ['Timestamp', 'Action Type', 'Admin User', 'Affected Entity', 'Entity Type', 'IP Address', 'Details']
    }

    // Mock file download
    const blob = new Blob([JSON.stringify(exportData.data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = exportData.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    setLoading(false)
    toast.success(`${selectedExportType} data exported successfully as ${exportFormat.toUpperCase()}`)
    setShowExportDialog(false)
  }

  const getQuickExportButton = (type: 'users' | 'subscriptions' | 'audit-logs', icon: React.ReactNode, title: string) => (
    <Button
      variant="outline"
      className="h-auto p-4 flex flex-col items-center gap-2"
      onClick={() => {
        setSelectedExportType(type)
        setShowExportDialog(true)
      }}
    >
      {icon}
      <span className="text-sm font-medium">{title}</span>
      <span className="text-xs text-muted-foreground">Export as CSV</span>
    </Button>
  )

  // Statistics
  const stats = {
    totalUsers: mockUsers.length,
    activeUsers: mockUsers.filter(u => u.subscriptionStatus === 'Active').length,
    totalRevenue: mockSubscriptions.reduce((sum, sub) => sum + sub.amount, 0),
    activeSubscriptions: mockSubscriptions.filter(s => s.status === 'Active').length
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Export data and view system analytics
          </p>
        </div>
        <Button onClick={() => setShowExportDialog(true)}>
          <Download className="mr-2 h-4 w-4" />
          Custom Export
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeUsers} active users
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              {((stats.activeSubscriptions / mockSubscriptions.length) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              From {mockSubscriptions.length} subscriptions
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Audit Events</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAuditLogs.length}</div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Export Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Quick Export
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {getQuickExportButton('users', <Users className="h-6 w-6" />, 'Export Users')}
            {getQuickExportButton('subscriptions', <CreditCard className="h-6 w-6" />, 'Export Subscriptions')}
            {getQuickExportButton('audit-logs', <Shield className="h-6 w-6" />, 'Export Audit Logs')}
          </div>
        </CardContent>
      </Card>

      {/* Data Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Data Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="users" className="space-y-4">
            <TabsList>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
              <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
            </TabsList>

            <TabsContent value="users" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Full Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Workspace</TableHead>
                      <TableHead>Plan Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userPreviewData.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.fullName}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{user.workspaceName}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.planType}</Badge>
                        </TableCell>
                        <TableCell>
                          <StatusBadge status={user.subscriptionStatus} />
                        </TableCell>
                        <TableCell>{formatDate(user.accountCreatedDate)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-sm text-muted-foreground">
                Showing 5 of {mockUsers.length} users. Export to see all data.
              </p>
            </TabsContent>

            <TabsContent value="subscriptions" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User Name</TableHead>
                      <TableHead>Workspace</TableHead>
                      <TableHead>Plan Type</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {subscriptionPreviewData.map((subscription) => (
                      <TableRow key={subscription.id}>
                        <TableCell className="font-medium">{subscription.userName}</TableCell>
                        <TableCell>{subscription.workspaceName}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{subscription.planType}</Badge>
                        </TableCell>
                        <TableCell>{formatDate(subscription.startDate)}</TableCell>
                        <TableCell>{formatDate(subscription.endDate)}</TableCell>
                        <TableCell>
                          <StatusBadge status={subscription.status} />
                        </TableCell>
                        <TableCell>{formatCurrency(subscription.amount)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-sm text-muted-foreground">
                Showing 5 of {mockSubscriptions.length} subscriptions. Export to see all data.
              </p>
            </TabsContent>

            <TabsContent value="audit-logs" className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>Action Type</TableHead>
                      <TableHead>Admin User</TableHead>
                      <TableHead>Affected Entity</TableHead>
                      <TableHead>Entity Type</TableHead>
                      <TableHead>Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {auditLogPreviewData.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>{formatDateTime(log.timestamp)}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{log.actionType}</Badge>
                        </TableCell>
                        <TableCell className="font-medium">{log.adminUser}</TableCell>
                        <TableCell>{log.affectedEntity}</TableCell>
                        <TableCell>
                          <Badge variant="secondary">{log.entityType}</Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate">{log.details}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <p className="text-sm text-muted-foreground">
                Showing 5 of {mockAuditLogs.length} audit logs. Export to see all data.
              </p>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Custom Export Dialog */}
      <FormDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        title="Custom Data Export"
        description="Configure your data export settings"
        onSubmit={handleExport}
        submitText={loading ? 'Exporting...' : 'Export Data'}
        loading={loading}
        size="lg"
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="export-type">Data Type</Label>
            <Select
              value={selectedExportType}
              onValueChange={(value: 'users' | 'subscriptions' | 'audit-logs') => setSelectedExportType(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select data type..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="users">Users ({mockUsers.length} records)</SelectItem>
                <SelectItem value="subscriptions">Subscriptions ({mockSubscriptions.length} records)</SelectItem>
                <SelectItem value="audit-logs">Audit Logs ({mockAuditLogs.length} records)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="export-format">Export Format</Label>
            <Select
              value={exportFormat}
              onValueChange={(value: 'csv' | 'xlsx' | 'json') => setExportFormat(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select format..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">CSV (Comma Separated Values)</SelectItem>
                <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                <SelectItem value="json">JSON (JavaScript Object Notation)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date (Optional)</Label>
              <Input
                id="start-date"
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-date">End Date (Optional)</Label>
              <Input
                id="end-date"
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
          </div>

          <div className="p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Export Summary</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Data Type: {selectedExportType.charAt(0).toUpperCase() + selectedExportType.slice(1)}</li>
              <li>• Format: {exportFormat.toUpperCase()}</li>
              <li>• Records: {
                selectedExportType === 'users' ? mockUsers.length :
                selectedExportType === 'subscriptions' ? mockSubscriptions.length :
                mockAuditLogs.length
              }</li>
              {(dateRange.start || dateRange.end) && (
                <li>• Date Range: {dateRange.start || 'Beginning'} to {dateRange.end || 'End'}</li>
              )}
            </ul>
          </div>
        </div>
      </FormDialog>
    </div>
  )
}
