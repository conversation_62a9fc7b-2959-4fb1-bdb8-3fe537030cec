import { type Subscription, type RenewalLog } from '../types/dashboard'

export const mockSubscriptions: Subscription[] = [
  {
    id: 'sub-1',
    userId: '1',
    userName: '<PERSON>',
    workspaceName: 'TechCorp Solutions',
    planType: 'Enterprise',
    startDate: '2024-01-15',
    endDate: '2025-01-15',
    status: 'Active',
    nextBillingDate: '2025-01-15',
    amount: 99.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'ENT-2024-001'
  },
  {
    id: 'sub-2',
    userId: '2',
    userName: '<PERSON>',
    workspaceName: 'Creative Design Studio',
    planType: 'Premium',
    startDate: '2024-06-20',
    endDate: '2025-06-20',
    status: 'Active',
    nextBillingDate: '2025-06-20',
    amount: 49.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'PRE-2024-002'
  },
  {
    id: 'sub-3',
    userId: '3',
    userName: '<PERSON>',
    workspaceName: 'StartupInc',
    planType: 'Basic',
    startDate: '2024-05-01',
    endDate: '2024-11-01',
    status: 'Expired',
    nextBillingDate: '2024-11-01',
    amount: 19.99,
    currency: 'USD',
    isAutoRenewal: false,
    referenceNumber: 'BAS-2024-003'
  },
  {
    id: 'sub-4',
    userId: '4',
    userName: 'Emily Rodriguez',
    workspaceName: 'Marketing Pro Agency',
    planType: 'Premium',
    startDate: '2024-07-01',
    endDate: '2025-07-01',
    status: 'Active',
    nextBillingDate: '2025-07-01',
    amount: 49.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'PRE-2024-004'
  },
  {
    id: 'sub-5',
    userId: '5',
    userName: 'David Wilson',
    workspaceName: 'Wilson Consulting',
    planType: 'Basic',
    startDate: '2024-03-15',
    endDate: '2024-09-15',
    status: 'Cancelled',
    nextBillingDate: '2024-09-15',
    amount: 19.99,
    currency: 'USD',
    isAutoRenewal: false,
    referenceNumber: 'BAS-2024-005'
  },
  {
    id: 'sub-6',
    userId: '6',
    userName: 'Lisa Thompson',
    workspaceName: 'E-Commerce Solutions',
    planType: 'Enterprise',
    startDate: '2024-02-28',
    endDate: '2025-02-28',
    status: 'Active',
    nextBillingDate: '2025-02-28',
    amount: 99.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'ENT-2024-006'
  },
  {
    id: 'sub-7',
    userId: '7',
    userName: 'Robert Garcia',
    workspaceName: 'Garcia & Associates',
    planType: 'Premium',
    startDate: '2024-04-10',
    endDate: '2025-04-10',
    status: 'Active',
    nextBillingDate: '2025-04-10',
    amount: 49.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'PRE-2024-007'
  },
  {
    id: 'sub-8',
    userId: '8',
    userName: 'Amanda Lee',
    workspaceName: 'HealthTech Innovations',
    planType: 'Premium',
    startDate: '2024-04-30',
    endDate: '2024-10-30',
    status: 'Expired',
    nextBillingDate: '2024-10-30',
    amount: 49.99,
    currency: 'USD',
    isAutoRenewal: false,
    referenceNumber: 'PRE-2024-008'
  },
  {
    id: 'sub-9',
    userId: '9',
    userName: 'James Brown',
    workspaceName: 'Brown Real Estate',
    planType: 'Basic',
    startDate: '2024-06-15',
    endDate: '2024-12-15',
    status: 'Active',
    nextBillingDate: '2024-12-15',
    amount: 19.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'BAS-2024-009'
  },
  {
    id: 'sub-10',
    userId: '10',
    userName: 'Jennifer Davis',
    workspaceName: 'Davis Financial Services',
    planType: 'Enterprise',
    startDate: '2024-05-20',
    endDate: '2025-05-20',
    status: 'Active',
    nextBillingDate: '2025-05-20',
    amount: 99.99,
    currency: 'USD',
    isAutoRenewal: true,
    referenceNumber: 'ENT-2024-010'
  }
]

export const mockRenewalLogs: RenewalLog[] = [
  {
    id: 'ren-1',
    subscriptionId: 'sub-1',
    userName: 'John Smith',
    workspaceName: 'TechCorp Solutions',
    planType: 'Enterprise',
    startDate: '2024-01-15',
    endDate: '2025-01-15',
    amount: 99.99,
    adminUser: '<EMAIL>',
    timestamp: '2024-01-15T10:30:00Z',
    referenceNumber: 'MAN-ENT-2024-001',
    notes: 'Manual renewal requested by customer'
  },
  {
    id: 'ren-2',
    subscriptionId: 'sub-4',
    userName: 'Emily Rodriguez',
    workspaceName: 'Marketing Pro Agency',
    planType: 'Premium',
    startDate: '2024-07-01',
    endDate: '2025-07-01',
    amount: 49.99,
    adminUser: '<EMAIL>',
    timestamp: '2024-07-01T14:15:00Z',
    referenceNumber: 'MAN-PRE-2024-002',
    notes: 'Upgraded from Basic to Premium'
  },
  {
    id: 'ren-3',
    subscriptionId: 'sub-6',
    userName: 'Lisa Thompson',
    workspaceName: 'E-Commerce Solutions',
    planType: 'Enterprise',
    startDate: '2024-02-28',
    endDate: '2025-02-28',
    amount: 99.99,
    adminUser: '<EMAIL>',
    timestamp: '2024-02-28T09:45:00Z',
    referenceNumber: 'MAN-ENT-2024-003',
    notes: 'Annual renewal with discount applied'
  }
]
