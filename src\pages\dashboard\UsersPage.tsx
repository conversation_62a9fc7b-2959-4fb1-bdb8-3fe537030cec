import { useState, useMemo } from 'react'
import { Users, UserPlus, Eye, UserX, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  DataTable, 
  SearchInput, 
  FilterDropdown, 
  ConfirmDialog, 
  FormDialog,
  StatusBadge 
} from '@/components/dashboard'
import { mockUsers } from '@/data'
import { User, TableColumn, FilterOption, PaginationInfo } from '@/types/dashboard'
import { toast } from 'sonner'

export function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPlanTypes, setSelectedPlanTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [sortColumn, setSortColumn] = useState<keyof User>('fullName')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showUserDetails, setShowUserDetails] = useState(false)
  const [showDisableConfirm, setShowDisableConfirm] = useState(false)
  const [showRenewalDialog, setShowRenewalDialog] = useState(false)
  const [loading, setLoading] = useState(false)

  const itemsPerPage = 10

  // Filter options
  const planTypeOptions: FilterOption[] = [
    { value: 'Basic', label: 'Basic', count: mockUsers.filter(u => u.planType === 'Basic').length },
    { value: 'Premium', label: 'Premium', count: mockUsers.filter(u => u.planType === 'Premium').length },
    { value: 'Enterprise', label: 'Enterprise', count: mockUsers.filter(u => u.planType === 'Enterprise').length }
  ]

  const statusOptions: FilterOption[] = [
    { value: 'Active', label: 'Active', count: mockUsers.filter(u => u.subscriptionStatus === 'Active').length },
    { value: 'Inactive', label: 'Inactive', count: mockUsers.filter(u => u.subscriptionStatus === 'Inactive').length },
    { value: 'Expired', label: 'Expired', count: mockUsers.filter(u => u.subscriptionStatus === 'Expired').length }
  ]

  // Filtered and sorted data
  const filteredData = useMemo(() => {
    let filtered = mockUsers.filter(user => {
      const matchesSearch = searchTerm === '' || 
        user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.workspaceName.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesPlanType = selectedPlanTypes.length === 0 || 
        selectedPlanTypes.includes(user.planType)
      
      const matchesStatus = selectedStatuses.length === 0 || 
        selectedStatuses.includes(user.subscriptionStatus)

      return matchesSearch && matchesPlanType && matchesStatus
    })

    // Sort data
    filtered.sort((a, b) => {
      const aValue = a[sortColumn]
      const bValue = b[sortColumn]
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [searchTerm, selectedPlanTypes, selectedStatuses, sortColumn, sortDirection])

  // Pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredData.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredData, currentPage])

  const pagination: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(filteredData.length / itemsPerPage),
    totalItems: filteredData.length,
    itemsPerPage
  }

  // Table columns
  const columns: TableColumn<User>[] = [
    {
      key: 'fullName',
      label: 'Full Name',
      sortable: true
    },
    {
      key: 'email',
      label: 'Email Address',
      sortable: true
    },
    {
      key: 'workspaceName',
      label: 'Workspace Name',
      sortable: true
    },
    {
      key: 'subscriptionStatus',
      label: 'Status',
      sortable: true,
      render: (value) => <StatusBadge status={value} />
    },
    {
      key: 'planType',
      label: 'Plan Type',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'lastRenewalDate',
      label: 'Last Renewal',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    }
  ]

  // Table actions
  const actions = [
    {
      label: 'View Details',
      onClick: (user: User) => {
        setSelectedUser(user)
        setShowUserDetails(true)
      }
    },
    {
      label: 'Disable Account',
      onClick: (user: User) => {
        setSelectedUser(user)
        setShowDisableConfirm(true)
      },
      variant: 'destructive' as const
    },
    {
      label: 'Renew Subscription',
      onClick: (user: User) => {
        setSelectedUser(user)
        setShowRenewalDialog(true)
      }
    }
  ]

  const handleDisableUser = async () => {
    setLoading(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`User ${selectedUser?.fullName} has been disabled`)
    setSelectedUser(null)
  }

  const handleRenewSubscription = async () => {
    setLoading(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`Subscription renewed for ${selectedUser?.fullName}`)
    setSelectedUser(null)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage user accounts, subscriptions, and access permissions
          </p>
        </div>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users ({filteredData.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <SearchInput
              placeholder="Search by name, email, or workspace..."
              value={searchTerm}
              onChange={setSearchTerm}
              className="flex-1"
            />
            <FilterDropdown
              label="Plan Type"
              options={planTypeOptions}
              selectedValues={selectedPlanTypes}
              onSelectionChange={setSelectedPlanTypes}
              className="w-full sm:w-auto"
            />
            <FilterDropdown
              label="Status"
              options={statusOptions}
              selectedValues={selectedStatuses}
              onSelectionChange={setSelectedStatuses}
              className="w-full sm:w-auto"
            />
          </div>

          <DataTable
            data={paginatedData}
            columns={columns}
            loading={loading}
            pagination={pagination}
            onPageChange={setCurrentPage}
            onSort={(column, direction) => {
              setSortColumn(column)
              setSortDirection(direction)
            }}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            actions={actions}
            emptyState={{
              title: 'No users found',
              description: 'No users match your current search and filter criteria.',
              action: {
                label: 'Clear filters',
                onClick: () => {
                  setSearchTerm('')
                  setSelectedPlanTypes([])
                  setSelectedStatuses([])
                }
              }
            }}
          />
        </CardContent>
      </Card>

      {/* User Details Dialog */}
      <FormDialog
        open={showUserDetails}
        onOpenChange={setShowUserDetails}
        title="User Details"
        description={`Viewing details for ${selectedUser?.fullName}`}
        onSubmit={() => setShowUserDetails(false)}
        submitText="Close"
        size="lg"
      >
        {selectedUser && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Full Name</label>
                <p className="text-sm text-muted-foreground">{selectedUser.fullName}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Email</label>
                <p className="text-sm text-muted-foreground">{selectedUser.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Workspace</label>
                <p className="text-sm text-muted-foreground">{selectedUser.workspaceName}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Plan Type</label>
                <p className="text-sm text-muted-foreground">{selectedUser.planType}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Status</label>
                <StatusBadge status={selectedUser.subscriptionStatus} />
              </div>
              <div>
                <label className="text-sm font-medium">Role</label>
                <p className="text-sm text-muted-foreground">{selectedUser.role}</p>
              </div>
            </div>
          </div>
        )}
      </FormDialog>

      {/* Disable User Confirmation */}
      <ConfirmDialog
        open={showDisableConfirm}
        onOpenChange={setShowDisableConfirm}
        title="Disable User Account"
        description={`Are you sure you want to disable ${selectedUser?.fullName}'s account? This will prevent them from accessing their workspace.`}
        confirmText="Disable Account"
        onConfirm={handleDisableUser}
        variant="destructive"
        loading={loading}
      />

      {/* Renewal Dialog */}
      <FormDialog
        open={showRenewalDialog}
        onOpenChange={setShowRenewalDialog}
        title="Renew Subscription"
        description={`Renew subscription for ${selectedUser?.fullName}`}
        onSubmit={handleRenewSubscription}
        submitText="Renew"
        loading={loading}
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            This will extend the subscription for {selectedUser?.fullName} by 12 months.
          </p>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Current Plan</label>
              <p className="text-sm text-muted-foreground">{selectedUser?.planType}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Current Status</label>
              {selectedUser && <StatusBadge status={selectedUser.subscriptionStatus} />}
            </div>
          </div>
        </div>
      </FormDialog>
    </div>
  )
}
