import type { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface AuthWrapperProps {
  children: ReactNode;
  className?: string;
}

export function AuthWrapper({ children, className }: AuthWrapperProps) {
  return (
    <div 
      className={cn(
        "flex min-h-screen overflow-hidden flex-col items-center justify-center p-4 relative ",
        className
      )}
      style={{
        background: 'radial-gradient(circle at center, #000000 0%, #0a0a0a 100%)',
        position: 'relative'
      }}
    >
      {/* Dot pattern overlay */}
      <div
        className="absolute inset-0 pointer-events-none z-0 opacity-20"
        style={{
          backgroundImage: 'radial-gradient(rgba(255, 255, 255, 0.2) 3px, transparent 1px)',
          backgroundSize: '10px 10px'
        }}
      />
        {children}
    </div>
  );
} 