import { User } from '@/types/dashboard'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { StatusBadge } from './StatusBadge'
import { Badge } from '@/components/ui/badge'

interface UserCardProps {
  user: User
  className?: string
}

export function UserCard({ user, className }: UserCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={user.avatar} alt={user.fullName} />
            <AvatarFallback className="text-lg">
              {getInitials(user.fullName)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="text-xl font-semibold">{user.fullName}</h3>
            <p className="text-muted-foreground">{user.email}</p>
            <div className="flex items-center gap-2 mt-2">
              <StatusBadge status={user.subscriptionStatus} />
              <Badge variant="outline">{user.planType}</Badge>
              <Badge variant="secondary">{user.role}</Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Workspace</p>
            <p className="font-medium">{user.workspaceName}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Last Login</p>
            <p className="font-medium">{formatDate(user.lastLoginDate)}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Account Created</p>
            <p className="font-medium">{formatDate(user.accountCreatedDate)}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Last Renewal</p>
            <p className="font-medium">{formatDate(user.lastRenewalDate)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
