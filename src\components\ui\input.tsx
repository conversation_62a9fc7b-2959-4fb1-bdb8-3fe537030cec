import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "relative w-full select-none appearance-none transition ease-in-out duration-200 h-10 rounded-xl px-3 text-base sm:text-sm text-white border-[2px] border-white/5 backdrop-blur-[25px] bg-origin-border bg-[url(&quot;/static/texture-btn.png&quot;),linear-gradient(104deg,rgba(253,253,253,0.05)_5%,rgba(240,240,228,0.1)_100%)] focus-visible:ring-1 focus-visible:ring-white/20 focus-visible:outline-hidden disabled:opacity-100 disabled:text-white/50 h-12 !rounded-2xl px-4",
        className
      )}
      {...props}
    />
  )
}

export { Input }
