import { type AuditLog } from '../types/dashboard'

export const mockAuditLogs: AuditLog[] = [
  {
    id: 'audit-1',
    timestamp: '2024-07-14T10:30:00Z',
    actionType: 'User Created',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'User',
    ipAddress: '*************',
    details: 'New user account created for TechCorp Solutions workspace',
    metadata: {
      workspaceName: 'TechCorp Solutions',
      planType: 'Enterprise',
      userRole: 'Owner'
    }
  },
  {
    id: 'audit-2',
    timestamp: '2024-07-14T09:15:00Z',
    actionType: 'Subscription Renewed',
    adminUser: '<EMAIL>',
    affectedEntity: 'sub-4',
    entityType: 'Subscription',
    ipAddress: '*************',
    details: 'Manual subscription renewal for Marketing Pro Agency',
    metadata: {
      subscriptionId: 'sub-4',
      planType: 'Premium',
      amount: 49.99,
      duration: '12 months'
    }
  },
  {
    id: 'audit-3',
    timestamp: '2024-07-13T16:45:00Z',
    actionType: 'User Disabled',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'User',
    ipAddress: '*************',
    details: 'User account disabled due to payment failure',
    metadata: {
      reason: 'Payment failure',
      workspaceName: 'Wilson Consulting',
      previousStatus: 'Active'
    }
  },
  {
    id: 'audit-4',
    timestamp: '2024-07-13T14:20:00Z',
    actionType: 'Plan Changed',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'Subscription',
    ipAddress: '*************',
    details: 'Plan upgraded from Basic to Premium',
    metadata: {
      previousPlan: 'Basic',
      newPlan: 'Premium',
      effectiveDate: '2024-07-01'
    }
  },
  {
    id: 'audit-5',
    timestamp: '2024-07-12T11:30:00Z',
    actionType: 'Workspace Created',
    adminUser: '<EMAIL>',
    affectedEntity: 'ws-12',
    entityType: 'Workspace',
    ipAddress: '*************',
    details: 'New workspace created: Green Fashion Brand',
    metadata: {
      workspaceName: 'Green Fashion Brand',
      ownerEmail: '<EMAIL>',
      planType: 'Basic'
    }
  },
  {
    id: 'audit-6',
    timestamp: '2024-07-12T08:45:00Z',
    actionType: 'User Enabled',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'User',
    ipAddress: '*************',
    details: 'User account re-enabled after payment resolution',
    metadata: {
      workspaceName: 'E-Commerce Solutions',
      previousStatus: 'Disabled',
      reason: 'Payment resolved'
    }
  },
  {
    id: 'audit-7',
    timestamp: '2024-07-11T15:10:00Z',
    actionType: 'Settings Updated',
    adminUser: '<EMAIL>',
    affectedEntity: 'system-settings',
    entityType: 'Settings',
    ipAddress: '*************',
    details: 'Email notification settings updated',
    metadata: {
      changedSettings: ['sendExpiryReminders', 'sendWeeklyDigest'],
      previousValues: { sendExpiryReminders: false, sendWeeklyDigest: false },
      newValues: { sendExpiryReminders: true, sendWeeklyDigest: true }
    }
  },
  {
    id: 'audit-8',
    timestamp: '2024-07-11T13:25:00Z',
    actionType: 'Workspace Archived',
    adminUser: '<EMAIL>',
    affectedEntity: 'ws-5',
    entityType: 'Workspace',
    ipAddress: '*************',
    details: 'Workspace archived: Wilson Consulting',
    metadata: {
      workspaceName: 'Wilson Consulting',
      reason: 'Inactive subscription',
      memberCount: 2
    }
  },
  {
    id: 'audit-9',
    timestamp: '2024-07-10T10:15:00Z',
    actionType: 'User Created',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'User',
    ipAddress: '*************',
    details: 'New user account created for Young Beauty Salon workspace',
    metadata: {
      workspaceName: 'Young Beauty Salon',
      planType: 'Basic',
      userRole: 'Owner'
    }
  },
  {
    id: 'audit-10',
    timestamp: '2024-07-10T09:30:00Z',
    actionType: 'Subscription Renewed',
    adminUser: '<EMAIL>',
    affectedEntity: 'sub-6',
    entityType: 'Subscription',
    ipAddress: '*************',
    details: 'Automatic subscription renewal for E-Commerce Solutions',
    metadata: {
      subscriptionId: 'sub-6',
      planType: 'Enterprise',
      amount: 99.99,
      renewalType: 'automatic'
    }
  },
  {
    id: 'audit-11',
    timestamp: '2024-07-09T16:40:00Z',
    actionType: 'Plan Changed',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'Subscription',
    ipAddress: '*************',
    details: 'Plan downgraded from Enterprise to Premium',
    metadata: {
      previousPlan: 'Enterprise',
      newPlan: 'Premium',
      reason: 'Customer request',
      effectiveDate: '2024-07-09'
    }
  },
  {
    id: 'audit-12',
    timestamp: '2024-07-09T14:55:00Z',
    actionType: 'User Disabled',
    adminUser: '<EMAIL>',
    affectedEntity: '<EMAIL>',
    entityType: 'User',
    ipAddress: '*************',
    details: 'User account disabled for policy violation',
    metadata: {
      reason: 'Policy violation',
      workspaceName: 'Miller Event Planning',
      violationType: 'Terms of Service'
    }
  }
]
