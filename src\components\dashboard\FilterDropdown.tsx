import { useState } from 'react'
import { Check, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { FilterOption } from '@/types/dashboard'
import { cn } from '@/lib/utils'

interface FilterDropdownProps {
  label: string
  options: FilterOption[]
  selectedValues: string[]
  onSelectionChange: (values: string[]) => void
  multiSelect?: boolean
  className?: string
}

export function FilterDropdown({
  label,
  options,
  selectedValues,
  onSelectionChange,
  multiSelect = true,
  className
}: FilterDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleSelectionChange = (value: string, checked: boolean) => {
    if (multiSelect) {
      if (checked) {
        onSelectionChange([...selectedValues, value])
      } else {
        onSelectionChange(selectedValues.filter(v => v !== value))
      }
    } else {
      onSelectionChange(checked ? [value] : [])
      setIsOpen(false)
    }
  }

  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return `All ${label}`
    }
    if (selectedValues.length === 1) {
      const option = options.find(opt => opt.value === selectedValues[0])
      return option?.label || selectedValues[0]
    }
    return `${selectedValues.length} selected`
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn('justify-between', className)}
        >
          {getDisplayText()}
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>{label}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {options.map((option) => (
          <DropdownMenuItem
            key={option.value}
            className="flex items-center space-x-2"
            onSelect={(e) => e.preventDefault()}
          >
            {multiSelect ? (
              <Checkbox
                checked={selectedValues.includes(option.value)}
                onCheckedChange={(checked) =>
                  handleSelectionChange(option.value, checked as boolean)
                }
              />
            ) : (
              <div className="w-4 h-4 flex items-center justify-center">
                {selectedValues.includes(option.value) && (
                  <Check className="h-4 w-4" />
                )}
              </div>
            )}
            <div className="flex-1 flex items-center justify-between">
              <span>{option.label}</span>
              {option.count !== undefined && (
                <span className="text-xs text-muted-foreground">
                  {option.count}
                </span>
              )}
            </div>
          </DropdownMenuItem>
        ))}
        {selectedValues.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onSelect={() => onSelectionChange([])}
              className="text-sm text-muted-foreground"
            >
              Clear all
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
