"use client"

import * as React from "react"
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  Users,
  Building2,
  CreditCard,
  Package,
  Shield,
  BarChart3,
  Settings,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Acme Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "Admin Dashboard",
      url: "/dashboard",
      icon: SquareTerminal,
      isActive: true,
      items: [
        {
          title: "Overview",
          url: "/dashboard",
        },
        {
          title: "Users",
          url: "/dashboard/users",
        },
        {
          title: "Workspaces",
          url: "/dashboard/workspaces",
        },
        {
          title: "Subscriptions",
          url: "/dashboard/subscriptions",
        },
      ],
    },
    {
      title: "User Management",
      url: "/dashboard/users",
      icon: Users,
      items: [
        {
          title: "All Users",
          url: "/dashboard/users",
        },
        {
          title: "User Details",
          url: "/dashboard/users",
        },
        {
          title: "Permissions",
          url: "/dashboard/users",
        },
      ],
    },
    {
      title: "Billing & Plans",
      url: "/dashboard/subscriptions",
      icon: CreditCard,
      items: [
        {
          title: "Subscriptions",
          url: "/dashboard/subscriptions",
        },
        {
          title: "Plans",
          url: "/dashboard/plans",
        },
        {
          title: "Renewals",
          url: "/dashboard/subscriptions",
        },
      ],
    },
    {
      title: "System",
      url: "/dashboard/settings",
      icon: Settings2,
      items: [
        {
          title: "Audit Logs",
          url: "/dashboard/audit-logs",
        },
        {
          title: "Reports",
          url: "/dashboard/reports",
        },
        {
          title: "Settings",
          url: "/dashboard/settings",
        },
      ],
    },
  ],
  projects: [
    {
      name: "Workspace Management",
      url: "/dashboard/workspaces",
      icon: Building2,
    },
    {
      name: "Plan Management",
      url: "/dashboard/plans",
      icon: Package,
    },
    {
      name: "Analytics & Reports",
      url: "/dashboard/reports",
      icon: BarChart3,
    },
    {
      name: "System Security",
      url: "/dashboard/audit-logs",
      icon: Shield,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props} variant="floating">
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
