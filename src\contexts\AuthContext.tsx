import { createContext, useContext, useState, useEffect, type ReactNode } from 'react'

interface User {
  email: string
  firstName?: string
  lastName?: string
}

interface AuthContextType {
  isAuthenticated: boolean
  user: User | null
  login: (email: string, firstName?: string, lastName?: string) => void
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    // Check if user is already authenticated on app load
    const authStatus = localStorage.getItem('isAuthenticated')
    const userData = localStorage.getItem('userData')
    
    if (authStatus === 'true' && userData) {
      try {
        const parsedUser = JSON.parse(userData)
        setIsAuthenticated(true)
        setUser(parsedUser)
      } catch (error) {
        // Fallback for old format
        const email = localStorage.getItem('userEmail')
        if (email) {
          setIsAuthenticated(true)
          setUser({ email })
        }
      }
    }
  }, [])

  const login = (email: string, firstName?: string, lastName?: string) => {
    const userData = { email, firstName, lastName }
    localStorage.setItem('isAuthenticated', 'true')
    localStorage.setItem('userData', JSON.stringify(userData))
    setIsAuthenticated(true)
    setUser(userData)
  }

  const logout = () => {
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('userData')
    localStorage.removeItem('userEmail') // Clean up old format
    setIsAuthenticated(false)
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 