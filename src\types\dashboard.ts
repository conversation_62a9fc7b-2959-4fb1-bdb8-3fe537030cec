export interface User {
  id: string
  fullName: string
  email: string
  workspaceName: string
  subscriptionStatus: 'Active' | 'Inactive' | 'Expired'
  planType: 'Basic' | 'Premium' | 'Enterprise'
  lastRenewalDate: string
  accountCreatedDate: string
  lastLoginDate: string
  isDisabled: boolean
  avatar?: string
  role: 'Owner' | 'Admin' | 'Member'
  permissions: string[]
}

export interface Workspace {
  id: string
  name: string
  ownerName: string
  ownerEmail: string
  planType: 'Basic' | 'Premium' | 'Enterprise'
  createdDate: string
  memberCount: number
  isArchived: boolean
  description?: string
  settings: {
    allowInvites: boolean
    requireApproval: boolean
    maxMembers: number
  }
}

export interface Subscription {
  id: string
  userId: string
  userName: string
  workspaceName: string
  planType: 'Basic' | 'Premium' | 'Enterprise'
  startDate: string
  endDate: string
  status: 'Active' | 'Inactive' | 'Expired' | 'Cancelled'
  nextBillingDate: string
  amount: number
  currency: string
  isAutoRenewal: boolean
  referenceNumber?: string
}

export interface Plan {
  id: string
  name: string
  monthlyPrice: number
  userLimit: number
  storageLimit: number // in GB
  apiCallsLimit: number
  features: string[]
  isActive: boolean
  isPopular?: boolean
  description?: string
}

export interface AuditLog {
  id: string
  timestamp: string
  actionType: 'User Created' | 'User Disabled' | 'User Enabled' | 'Plan Changed' | 'Subscription Renewed' | 'Workspace Created' | 'Workspace Archived' | 'Settings Updated'
  adminUser: string
  affectedEntity: string
  entityType: 'User' | 'Workspace' | 'Subscription' | 'Plan' | 'Settings'
  ipAddress: string
  details: string
  metadata?: Record<string, any>
}

export interface RenewalLog {
  id: string
  subscriptionId: string
  userName: string
  workspaceName: string
  planType: string
  startDate: string
  endDate: string
  amount: number
  adminUser: string
  timestamp: string
  referenceNumber: string
  notes?: string
}

export interface SystemSettings {
  emailNotifications: {
    sendActivationEmails: boolean
    sendExpiryReminders: boolean
    sendRenewalConfirmations: boolean
    sendWeeklyDigest: boolean
  }
  general: {
    maintenanceMode: boolean
    allowNewRegistrations: boolean
    requireEmailVerification: boolean
  }
}

export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
}

export interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
}

export interface FilterOption {
  value: string
  label: string
  count?: number
}

export interface SearchFilters {
  searchTerm: string
  planType?: string
  status?: string
  dateRange?: {
    start: string
    end: string
  }
}

export interface ExportData {
  type: 'users' | 'subscriptions' | 'audit-logs'
  filename: string
  data: any[]
  columns: string[]
}
