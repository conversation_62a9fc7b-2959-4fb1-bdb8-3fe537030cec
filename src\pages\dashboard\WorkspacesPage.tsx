import { useState, useMemo } from 'react'
import { Building2, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  DataTable, 
  SearchInput, 
  FilterDropdown, 
  ConfirmDialog, 
  FormDialog,

} from '@/components/dashboard'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { mockWorkspaces } from '@/data'
import { type Workspace, type TableColumn, type FilterOption, type PaginationInfo } from '@/types/dashboard'
import { toast } from 'sonner'

export function WorkspacesPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPlanTypes, setSelectedPlanTypes] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [sortColumn, setSortColumn] = useState<keyof Workspace>('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null)
  const [showMembersDialog, setShowMembersDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false)
  const [loading, setLoading] = useState(false)

  const itemsPerPage = 10

  // Filter options
  const planTypeOptions: FilterOption[] = [
    { value: 'Basic', label: 'Basic', count: mockWorkspaces.filter(w => w.planType === 'Basic').length },
    { value: 'Premium', label: 'Premium', count: mockWorkspaces.filter(w => w.planType === 'Premium').length },
    { value: 'Enterprise', label: 'Enterprise', count: mockWorkspaces.filter(w => w.planType === 'Enterprise').length }
  ]

  // Filtered and sorted data
  const filteredData = useMemo(() => {
    let filtered = mockWorkspaces.filter(workspace => {
      const matchesSearch = searchTerm === '' || 
        workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workspace.ownerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workspace.ownerEmail.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesPlanType = selectedPlanTypes.length === 0 || 
        selectedPlanTypes.includes(workspace.planType)

      return matchesSearch && matchesPlanType
    })

    // Sort data
    filtered.sort((a, b) => {
      const aValue = a[sortColumn]
      const bValue = b[sortColumn]

      if (aValue == null && bValue == null) return 0
      if (aValue == null) return 1
      if (bValue == null) return -1

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [searchTerm, selectedPlanTypes, sortColumn, sortDirection])

  // Pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredData.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredData, currentPage])

  const pagination: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(filteredData.length / itemsPerPage),
    totalItems: filteredData.length,
    itemsPerPage
  }

  // Table columns
  const columns: TableColumn<Workspace>[] = [
    {
      key: 'name',
      label: 'Workspace Name',
      sortable: true,
      render: (value, workspace) => (
        <div className="flex items-center gap-2">
          <span className="font-medium">{value}</span>
          {workspace.isArchived && <Badge variant="secondary">Archived</Badge>}
        </div>
      )
    },
    {
      key: 'ownerName',
      label: 'Owner Name',
      sortable: true
    },
    {
      key: 'ownerEmail',
      label: 'Owner Email',
      sortable: true,
      render: (value) => (
        <span className="text-muted-foreground">{value}</span>
      )
    },
    {
      key: 'planType',
      label: 'Plan Type',
      sortable: true,
      render: (value) => (
        <Badge variant="outline">{value}</Badge>
      )
    },
    {
      key: 'createdDate',
      label: 'Created Date',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'memberCount',
      label: 'Members',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-1">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span>{value}</span>
        </div>
      )
    }
  ]

  // Table actions
  const actions = [
    {
      label: 'View Members',
      onClick: (workspace: Workspace) => {
        setSelectedWorkspace(workspace)
        setShowMembersDialog(true)
      }
    },
    {
      label: 'Edit Workspace',
      onClick: (workspace: Workspace) => {
        setSelectedWorkspace(workspace)
        setShowEditDialog(true)
      }
    },
    {
      label: 'Archive Workspace',
      onClick: (workspace: Workspace) => {
        setSelectedWorkspace(workspace)
        setShowArchiveConfirm(true)
      },
      variant: 'destructive' as const
    }
  ]

  const handleArchiveWorkspace = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`Workspace ${selectedWorkspace?.name} has been archived`)
    setSelectedWorkspace(null)
  }

  const handleSaveWorkspace = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`Workspace ${selectedWorkspace?.name} has been updated`)
    setSelectedWorkspace(null)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workspace Management</h1>
          <p className="text-muted-foreground">
            Manage workspaces, members, and workspace settings
          </p>
        </div>
        <Button>
          <Building2 className="mr-2 h-4 w-4" />
          Create Workspace
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Workspaces ({filteredData.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <SearchInput
              placeholder="Search by workspace name, owner name, or email..."
              value={searchTerm}
              onChange={setSearchTerm}
              className="flex-1"
            />
            <FilterDropdown
              label="Plan Type"
              options={planTypeOptions}
              selectedValues={selectedPlanTypes}
              onSelectionChange={setSelectedPlanTypes}
              className="w-full sm:w-auto"
            />
          </div>

          <DataTable
            data={paginatedData}
            columns={columns}
            loading={loading}
            pagination={pagination}
            onPageChange={setCurrentPage}
            onSort={(column, direction) => {
              setSortColumn(column)
              setSortDirection(direction)
            }}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            actions={actions}
            emptyState={{
              title: 'No workspaces found',
              description: 'No workspaces match your current search and filter criteria.',
              action: {
                label: 'Clear filters',
                onClick: () => {
                  setSearchTerm('')
                  setSelectedPlanTypes([])
                }
              }
            }}
          />
        </CardContent>
      </Card>

      {/* View Members Dialog */}
      <FormDialog
        open={showMembersDialog}
        onOpenChange={setShowMembersDialog}
        title="Workspace Members"
        description={`Members of ${selectedWorkspace?.name}`}
        onSubmit={() => setShowMembersDialog(false)}
        submitText="Close"
        size="lg"
      >
        {selectedWorkspace && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Total Members</label>
                <p className="text-2xl font-bold">{selectedWorkspace.memberCount}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Max Members</label>
                <p className="text-2xl font-bold">{selectedWorkspace.settings.maxMembers}</p>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Settings</label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Allow Invites</span>
                  <Badge variant={selectedWorkspace.settings.allowInvites ? 'default' : 'secondary'}>
                    {selectedWorkspace.settings.allowInvites ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Require Approval</span>
                  <Badge variant={selectedWorkspace.settings.requireApproval ? 'default' : 'secondary'}>
                    {selectedWorkspace.settings.requireApproval ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}
      </FormDialog>

      {/* Edit Workspace Dialog */}
      <FormDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        title="Edit Workspace"
        description={`Edit settings for ${selectedWorkspace?.name}`}
        onSubmit={handleSaveWorkspace}
        submitText="Save Changes"
        loading={loading}
        size="lg"
      >
        {selectedWorkspace && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="workspace-name">Workspace Name</Label>
                <Input
                  id="workspace-name"
                  defaultValue={selectedWorkspace.name}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-members">Max Members</Label>
                <Input
                  id="max-members"
                  type="number"
                  defaultValue={selectedWorkspace.settings.maxMembers}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                defaultValue={selectedWorkspace.description}
                placeholder="Workspace description..."
              />
            </div>

            <div className="space-y-4">
              <Label>Workspace Settings</Label>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Allow Invites</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow workspace members to invite new users
                    </p>
                  </div>
                  <Switch defaultChecked={selectedWorkspace.settings.allowInvites} />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Approval</Label>
                    <p className="text-sm text-muted-foreground">
                      Require admin approval for new member invitations
                    </p>
                  </div>
                  <Switch defaultChecked={selectedWorkspace.settings.requireApproval} />
                </div>
              </div>
            </div>
          </div>
        )}
      </FormDialog>

      {/* Archive Confirmation */}
      <ConfirmDialog
        open={showArchiveConfirm}
        onOpenChange={setShowArchiveConfirm}
        title="Archive Workspace"
        description={`Are you sure you want to archive ${selectedWorkspace?.name}? This will disable access for all members.`}
        confirmText="Archive Workspace"
        onConfirm={handleArchiveWorkspace}
        variant="destructive"
        loading={loading}
      />
    </div>
  )
}
