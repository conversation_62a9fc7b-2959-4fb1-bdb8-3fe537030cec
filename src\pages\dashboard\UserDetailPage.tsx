import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { ArrowLeft, UserX, User<PERSON>heck, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { UserCard, ConfirmDialog, StatusBadge } from '@/components/dashboard'
import { mockUsers, mockSubscriptions } from '@/data'
import { toast } from 'sonner'

export function UserDetailPage() {
  const { id } = useParams<{ id: string }>()
  const [showDisableConfirm, setShowDisableConfirm] = useState(false)
  const [showEnableConfirm, setShowEnableConfirm] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [loading, setLoading] = useState(false)

  // Find user by ID
  const user = mockUsers.find(u => u.id === id)
  
  if (!user) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link to="/dashboard/users">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">User Not Found</h3>
              <p className="text-muted-foreground">The requested user could not be found.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Get user's subscription history
  const userSubscriptions = mockSubscriptions.filter(sub => sub.userId === user.id)

  const handleDisableUser = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`User ${user.fullName} has been disabled`)
  }

  const handleEnableUser = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`User ${user.fullName} has been enabled`)
  }

  const handleDeleteUser = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`User ${user.fullName} has been soft deleted`)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/dashboard">Dashboard</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/dashboard/users">Users</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>{user.fullName}</BreadcrumbPage>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" asChild>
            <Link to="/dashboard/users">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Link>
          </Button>
        </div>
      </div>

      {/* User Profile Card */}
      <UserCard user={user} />

      {/* Action Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Account Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {user.isDisabled ? (
              <Button
                variant="default"
                onClick={() => setShowEnableConfirm(true)}
              >
                <UserCheck className="mr-2 h-4 w-4" />
                Enable Account
              </Button>
            ) : (
              <Button
                variant="destructive"
                onClick={() => setShowDisableConfirm(true)}
              >
                <UserX className="mr-2 h-4 w-4" />
                Disable Account
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(true)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Soft Delete
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tabbed Interface */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workspace">Workspace Details</TabsTrigger>
          <TabsTrigger value="subscription">Subscription History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Account Created</label>
                  <p className="text-lg font-semibold">{formatDate(user.accountCreatedDate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Login</label>
                  <p className="text-lg font-semibold">{formatDate(user.lastLoginDate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Current Plan</label>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{user.planType}</Badge>
                    <StatusBadge status={user.subscriptionStatus} />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Renewal</label>
                  <p className="text-lg font-semibold">{formatDate(user.lastRenewalDate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Account Status</label>
                  <p className="text-lg font-semibold">
                    {user.isDisabled ? 'Disabled' : 'Active'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">User Role</label>
                  <Badge>{user.role}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workspace" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workspace Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Workspace Name</label>
                  <p className="text-lg font-semibold">{user.workspaceName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">User Role</label>
                  <Badge>{user.role}</Badge>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-muted-foreground">Permissions</label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {user.permissions.map((permission) => (
                    <Badge key={permission} variant="secondary">
                      {permission.replace('_', ' ')}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscription" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Subscription History</CardTitle>
            </CardHeader>
            <CardContent>
              {userSubscriptions.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userSubscriptions.map((subscription) => (
                      <TableRow key={subscription.id}>
                        <TableCell>{formatDate(subscription.startDate)}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{subscription.planType}</Badge>
                        </TableCell>
                        <TableCell>
                          {formatDate(subscription.startDate)} - {formatDate(subscription.endDate)}
                        </TableCell>
                        <TableCell>
                          <StatusBadge status={subscription.status} />
                        </TableCell>
                        <TableCell>{formatCurrency(subscription.amount)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No subscription history available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Confirmation Dialogs */}
      <ConfirmDialog
        open={showDisableConfirm}
        onOpenChange={setShowDisableConfirm}
        title="Disable User Account"
        description={`Are you sure you want to disable ${user.fullName}'s account? This will prevent them from accessing their workspace.`}
        confirmText="Disable Account"
        onConfirm={handleDisableUser}
        variant="destructive"
        loading={loading}
      />

      <ConfirmDialog
        open={showEnableConfirm}
        onOpenChange={setShowEnableConfirm}
        title="Enable User Account"
        description={`Are you sure you want to enable ${user.fullName}'s account? This will restore their access to the workspace.`}
        confirmText="Enable Account"
        onConfirm={handleEnableUser}
        loading={loading}
      />

      <ConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Soft Delete User"
        description={`Are you sure you want to soft delete ${user.fullName}'s account? This action can be reversed later.`}
        confirmText="Soft Delete"
        onConfirm={handleDeleteUser}
        variant="destructive"
        loading={loading}
      />
    </div>
  )
}
