import { useState } from 'react'
import { Package, Plus, Edit, Eye, EyeOff, Star } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { FormDialog, ConfirmDialog } from '@/components/dashboard'
import { mockPlans } from '@/data'
import { Plan } from '@/types/dashboard'
import { toast } from 'sonner'

export function PlansPage() {
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showToggleConfirm, setShowToggleConfirm] = useState(false)
  const [loading, setLoading] = useState(false)

  // Form state for create/edit
  const [planForm, setPlanForm] = useState({
    name: '',
    monthlyPrice: 0,
    userLimit: 0,
    storageLimit: 0,
    apiCallsLimit: 0,
    description: '',
    isActive: true,
    isPopular: false,
    features: ['']
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const resetForm = () => {
    setPlanForm({
      name: '',
      monthlyPrice: 0,
      userLimit: 0,
      storageLimit: 0,
      apiCallsLimit: 0,
      description: '',
      isActive: true,
      isPopular: false,
      features: ['']
    })
  }

  const handleCreatePlan = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success('Plan created successfully')
    setShowCreateDialog(false)
    resetForm()
  }

  const handleEditPlan = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success(`Plan ${selectedPlan?.name} updated successfully`)
    setShowEditDialog(false)
    setSelectedPlan(null)
    resetForm()
  }

  const handleTogglePlan = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    const action = selectedPlan?.isActive ? 'disabled' : 'enabled'
    toast.success(`Plan ${selectedPlan?.name} has been ${action}`)
    setShowToggleConfirm(false)
    setSelectedPlan(null)
  }

  const openEditDialog = (plan: Plan) => {
    setSelectedPlan(plan)
    setPlanForm({
      name: plan.name,
      monthlyPrice: plan.monthlyPrice,
      userLimit: plan.userLimit,
      storageLimit: plan.storageLimit,
      apiCallsLimit: plan.apiCallsLimit,
      description: plan.description || '',
      isActive: plan.isActive,
      isPopular: plan.isPopular || false,
      features: plan.features
    })
    setShowEditDialog(true)
  }

  const addFeature = () => {
    setPlanForm(prev => ({
      ...prev,
      features: [...prev.features, '']
    }))
  }

  const updateFeature = (index: number, value: string) => {
    setPlanForm(prev => ({
      ...prev,
      features: prev.features.map((feature, i) => i === index ? value : feature)
    }))
  }

  const removeFeature = (index: number) => {
    setPlanForm(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  const PlanCard = ({ plan }: { plan: Plan }) => (
    <Card className={`relative ${plan.isPopular ? 'ring-2 ring-primary' : ''}`}>
      {plan.isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-primary text-primary-foreground">
            <Star className="w-3 h-3 mr-1" />
            Popular
          </Badge>
        </div>
      )}
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-between">
          <span>{plan.name}</span>
          <div className="flex items-center gap-2">
            {!plan.isActive && <Badge variant="secondary">Inactive</Badge>}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openEditDialog(plan)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedPlan(plan)
                setShowToggleConfirm(true)
              }}
            >
              {plan.isActive ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
          </div>
        </CardTitle>
        <div className="text-3xl font-bold">
          {formatCurrency(plan.monthlyPrice)}
          <span className="text-sm font-normal text-muted-foreground">/month</span>
        </div>
        {plan.description && (
          <p className="text-sm text-muted-foreground">{plan.description}</p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Users</span>
            <span className="font-medium">{plan.userLimit}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Storage</span>
            <span className="font-medium">{plan.storageLimit}GB</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>API Calls</span>
            <span className="font-medium">{plan.apiCallsLimit.toLocaleString()}/month</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Features</h4>
          <ul className="space-y-1">
            {plan.features.slice(0, 4).map((feature, index) => (
              <li key={index} className="text-sm text-muted-foreground flex items-center">
                <span className="w-1 h-1 bg-current rounded-full mr-2" />
                {feature}
              </li>
            ))}
            {plan.features.length > 4 && (
              <li className="text-sm text-muted-foreground">
                +{plan.features.length - 4} more features
              </li>
            )}
          </ul>
        </div>
      </CardContent>
    </Card>
  )

  const PlanFormFields = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="plan-name">Plan Name</Label>
          <Input
            id="plan-name"
            value={planForm.name}
            onChange={(e) => setPlanForm(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter plan name..."
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="monthly-price">Monthly Price ($)</Label>
          <Input
            id="monthly-price"
            type="number"
            step="0.01"
            value={planForm.monthlyPrice}
            onChange={(e) => setPlanForm(prev => ({ ...prev, monthlyPrice: parseFloat(e.target.value) || 0 }))}
            placeholder="0.00"
          />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="user-limit">User Limit</Label>
          <Input
            id="user-limit"
            type="number"
            value={planForm.userLimit}
            onChange={(e) => setPlanForm(prev => ({ ...prev, userLimit: parseInt(e.target.value) || 0 }))}
            placeholder="0"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="storage-limit">Storage Limit (GB)</Label>
          <Input
            id="storage-limit"
            type="number"
            value={planForm.storageLimit}
            onChange={(e) => setPlanForm(prev => ({ ...prev, storageLimit: parseInt(e.target.value) || 0 }))}
            placeholder="0"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="api-calls-limit">API Calls Limit</Label>
          <Input
            id="api-calls-limit"
            type="number"
            value={planForm.apiCallsLimit}
            onChange={(e) => setPlanForm(prev => ({ ...prev, apiCallsLimit: parseInt(e.target.value) || 0 }))}
            placeholder="0"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={planForm.description}
          onChange={(e) => setPlanForm(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Plan description..."
        />
      </div>

      <div className="space-y-4">
        <Label>Plan Settings</Label>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Active Plan</Label>
              <p className="text-sm text-muted-foreground">
                Make this plan available for new subscriptions
              </p>
            </div>
            <Switch
              checked={planForm.isActive}
              onCheckedChange={(checked) => setPlanForm(prev => ({ ...prev, isActive: checked }))}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Popular Plan</Label>
              <p className="text-sm text-muted-foreground">
                Mark this plan as popular (recommended)
              </p>
            </div>
            <Switch
              checked={planForm.isPopular}
              onCheckedChange={(checked) => setPlanForm(prev => ({ ...prev, isPopular: checked }))}
            />
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>Features</Label>
          <Button type="button" variant="outline" size="sm" onClick={addFeature}>
            <Plus className="h-4 w-4 mr-1" />
            Add Feature
          </Button>
        </div>
        <div className="space-y-2">
          {planForm.features.map((feature, index) => (
            <div key={index} className="flex gap-2">
              <Input
                value={feature}
                onChange={(e) => updateFeature(index, e.target.value)}
                placeholder="Enter feature..."
                className="flex-1"
              />
              {planForm.features.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeFeature(index)}
                >
                  Remove
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Plan Management</h1>
          <p className="text-muted-foreground">
            Create and manage subscription plans and pricing
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create New Plan
        </Button>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockPlans.map((plan) => (
          <PlanCard key={plan.id} plan={plan} />
        ))}
      </div>

      {/* Create Plan Dialog */}
      <FormDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        title="Create New Plan"
        description="Create a new subscription plan with custom features and pricing"
        onSubmit={handleCreatePlan}
        submitText="Create Plan"
        loading={loading}
        size="lg"
      >
        <PlanFormFields />
      </FormDialog>

      {/* Edit Plan Dialog */}
      <FormDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        title="Edit Plan"
        description={`Edit settings for ${selectedPlan?.name} plan`}
        onSubmit={handleEditPlan}
        submitText="Save Changes"
        loading={loading}
        size="lg"
      >
        <PlanFormFields />
      </FormDialog>

      {/* Toggle Plan Confirmation */}
      <ConfirmDialog
        open={showToggleConfirm}
        onOpenChange={setShowToggleConfirm}
        title={`${selectedPlan?.isActive ? 'Disable' : 'Enable'} Plan`}
        description={`Are you sure you want to ${selectedPlan?.isActive ? 'disable' : 'enable'} the ${selectedPlan?.name} plan? ${selectedPlan?.isActive ? 'This will prevent new subscriptions to this plan.' : 'This will make the plan available for new subscriptions.'}`}
        confirmText={selectedPlan?.isActive ? 'Disable Plan' : 'Enable Plan'}
        onConfirm={handleTogglePlan}
        variant={selectedPlan?.isActive ? 'destructive' : 'default'}
        loading={loading}
      />
    </div>
  )
}
