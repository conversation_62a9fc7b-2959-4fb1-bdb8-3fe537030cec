import { useState } from 'react'
import { Settings, Mail, Shield, Save, RotateCcw } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { mockSystemSettings, updateSystemSettings } from '@/data'
import { type SystemSettings } from '@/types/dashboard'
import { toast } from 'sonner'

export function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(mockSystemSettings)
  const [loading, setSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Additional settings state
  const [smtpSettings, setSmtpSettings] = useState({
    host: 'smtp.becosmartai.com',
    port: 587,
    username: '<EMAIL>',
    password: '••••••••',
    encryption: 'TLS'
  })

  const [maintenanceSettings, setMaintenanceSettings] = useState({
    message: 'We are currently performing scheduled maintenance. Please check back soon.',
    estimatedDuration: '2 hours',
    contactEmail: '<EMAIL>'
  })

  const updateEmailNotification = (key: keyof SystemSettings['emailNotifications'], value: boolean) => {
    setSettings(prev => ({
      ...prev,
      emailNotifications: {
        ...prev.emailNotifications,
        [key]: value
      }
    }))
    setHasChanges(true)
  }

  const updateGeneralSetting = (key: keyof SystemSettings['general'], value: boolean) => {
    setSettings(prev => ({
      ...prev,
      general: {
        ...prev.general,
        [key]: value
      }
    }))
    setHasChanges(true)
  }

  const handleSaveSettings = async () => {
    setSaving(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update settings
    updateSystemSettings(settings)
    
    setSaving(false)
    setHasChanges(false)
    toast.success('Settings saved successfully')
  }

  const handleResetSettings = () => {
    setSettings(mockSystemSettings)
    setHasChanges(false)
    toast.info('Settings reset to defaults')
  }

  const testEmailSettings = async () => {
    setSaving(true)
    await new Promise(resolve => setTimeout(resolve, 1500))
    setSaving(false)
    toast.success('Test email sent successfully')
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-muted-foreground">
            Configure system-wide settings and preferences
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Badge variant="secondary" className="mr-2">
              Unsaved changes
            </Badge>
          )}
          <Button
            variant="outline"
            onClick={handleResetSettings}
            disabled={loading}
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>
          <Button
            onClick={handleSaveSettings}
            disabled={loading || !hasChanges}
          >
            <Save className="mr-2 h-4 w-4" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Email Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="activation-emails">Send account activation emails</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically send welcome emails when new accounts are created
                </p>
              </div>
              <Switch
                id="activation-emails"
                checked={settings.emailNotifications.sendActivationEmails}
                onCheckedChange={(checked) => updateEmailNotification('sendActivationEmails', checked)}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="expiry-reminders">Send subscription expiry reminders (7 days before)</Label>
                <p className="text-sm text-muted-foreground">
                  Notify users when their subscription is about to expire
                </p>
              </div>
              <Switch
                id="expiry-reminders"
                checked={settings.emailNotifications.sendExpiryReminders}
                onCheckedChange={(checked) => updateEmailNotification('sendExpiryReminders', checked)}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="renewal-confirmations">Send manual renewal confirmation emails</Label>
                <p className="text-sm text-muted-foreground">
                  Send confirmation emails when subscriptions are manually renewed
                </p>
              </div>
              <Switch
                id="renewal-confirmations"
                checked={settings.emailNotifications.sendRenewalConfirmations}
                onCheckedChange={(checked) => updateEmailNotification('sendRenewalConfirmations', checked)}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="weekly-digest">Send weekly admin digest emails</Label>
                <p className="text-sm text-muted-foreground">
                  Weekly summary of system activity and statistics
                </p>
              </div>
              <Switch
                id="weekly-digest"
                checked={settings.emailNotifications.sendWeeklyDigest}
                onCheckedChange={(checked) => updateEmailNotification('sendWeeklyDigest', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* General System Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Enable maintenance mode to prevent user access during updates
                </p>
              </div>
              <Switch
                id="maintenance-mode"
                checked={settings.general.maintenanceMode}
                onCheckedChange={(checked) => updateGeneralSetting('maintenanceMode', checked)}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="allow-registrations">Allow new registrations</Label>
                <p className="text-sm text-muted-foreground">
                  Allow new users to create accounts and workspaces
                </p>
              </div>
              <Switch
                id="allow-registrations"
                checked={settings.general.allowNewRegistrations}
                onCheckedChange={(checked) => updateGeneralSetting('allowNewRegistrations', checked)}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-verification">Require email verification</Label>
                <p className="text-sm text-muted-foreground">
                  Require users to verify their email address before accessing the system
                </p>
              </div>
              <Switch
                id="email-verification"
                checked={settings.general.requireEmailVerification}
                onCheckedChange={(checked) => updateGeneralSetting('requireEmailVerification', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SMTP Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            SMTP Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="smtp-host">SMTP Host</Label>
              <Input
                id="smtp-host"
                value={smtpSettings.host}
                onChange={(e) => setSmtpSettings(prev => ({ ...prev, host: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="smtp-port">Port</Label>
              <Input
                id="smtp-port"
                type="number"
                value={smtpSettings.port}
                onChange={(e) => setSmtpSettings(prev => ({ ...prev, port: parseInt(e.target.value) }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="smtp-username">Username</Label>
              <Input
                id="smtp-username"
                value={smtpSettings.username}
                onChange={(e) => setSmtpSettings(prev => ({ ...prev, username: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="smtp-password">Password</Label>
              <Input
                id="smtp-password"
                type="password"
                value={smtpSettings.password}
                onChange={(e) => setSmtpSettings(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={testEmailSettings} disabled={loading}>
              {loading ? 'Testing...' : 'Test Email Settings'}
            </Button>
            <Badge variant="outline">{smtpSettings.encryption}</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Mode Configuration */}
      {settings.general.maintenanceMode && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Maintenance Mode Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="maintenance-message">Maintenance Message</Label>
              <Textarea
                id="maintenance-message"
                value={maintenanceSettings.message}
                onChange={(e) => setMaintenanceSettings(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Message to display to users during maintenance..."
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="estimated-duration">Estimated Duration</Label>
                <Input
                  id="estimated-duration"
                  value={maintenanceSettings.estimatedDuration}
                  onChange={(e) => setMaintenanceSettings(prev => ({ ...prev, estimatedDuration: e.target.value }))}
                  placeholder="e.g., 2 hours"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-email">Contact Email</Label>
                <Input
                  id="contact-email"
                  type="email"
                  value={maintenanceSettings.contactEmail}
                  onChange={(e) => setMaintenanceSettings(prev => ({ ...prev, contactEmail: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">99.9%</div>
              <p className="text-sm text-muted-foreground">Uptime</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">24ms</div>
              <p className="text-sm text-muted-foreground">Avg Response</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-sm text-muted-foreground">Active Sessions</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">15.2GB</div>
              <p className="text-sm text-muted-foreground">Storage Used</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
