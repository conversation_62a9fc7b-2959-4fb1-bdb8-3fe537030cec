import { useState, useMemo } from 'react'
import { Shield, Calendar, Filter, Download } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  DataTable, 
  SearchInput, 
  FilterDropdown, 
  FormDialog 
} from '@/components/dashboard'
import { mockAuditLogs } from '@/data'
import { AuditLog, TableColumn, FilterOption, PaginationInfo } from '@/types/dashboard'
import { toast } from 'sonner'

export function AuditLogsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedActionTypes, setSelectedActionTypes] = useState<string[]>([])
  const [selectedEntityTypes, setSelectedEntityTypes] = useState<string[]>([])
  const [dateRange, setDateRange] = useState({ start: '', end: '' })
  const [currentPage, setCurrentPage] = useState(1)
  const [sortColumn, setSortColumn] = useState<keyof AuditLog>('timestamp')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [showFilterDialog, setShowFilterDialog] = useState(false)
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [loading, setLoading] = useState(false)

  const itemsPerPage = 15

  // Filter options
  const actionTypeOptions: FilterOption[] = [
    { value: 'User Created', label: 'User Created', count: mockAuditLogs.filter(log => log.actionType === 'User Created').length },
    { value: 'User Disabled', label: 'User Disabled', count: mockAuditLogs.filter(log => log.actionType === 'User Disabled').length },
    { value: 'User Enabled', label: 'User Enabled', count: mockAuditLogs.filter(log => log.actionType === 'User Enabled').length },
    { value: 'Plan Changed', label: 'Plan Changed', count: mockAuditLogs.filter(log => log.actionType === 'Plan Changed').length },
    { value: 'Subscription Renewed', label: 'Subscription Renewed', count: mockAuditLogs.filter(log => log.actionType === 'Subscription Renewed').length },
    { value: 'Workspace Created', label: 'Workspace Created', count: mockAuditLogs.filter(log => log.actionType === 'Workspace Created').length },
    { value: 'Workspace Archived', label: 'Workspace Archived', count: mockAuditLogs.filter(log => log.actionType === 'Workspace Archived').length },
    { value: 'Settings Updated', label: 'Settings Updated', count: mockAuditLogs.filter(log => log.actionType === 'Settings Updated').length }
  ]

  const entityTypeOptions: FilterOption[] = [
    { value: 'User', label: 'User', count: mockAuditLogs.filter(log => log.entityType === 'User').length },
    { value: 'Workspace', label: 'Workspace', count: mockAuditLogs.filter(log => log.entityType === 'Workspace').length },
    { value: 'Subscription', label: 'Subscription', count: mockAuditLogs.filter(log => log.entityType === 'Subscription').length },
    { value: 'Plan', label: 'Plan', count: mockAuditLogs.filter(log => log.entityType === 'Plan').length },
    { value: 'Settings', label: 'Settings', count: mockAuditLogs.filter(log => log.entityType === 'Settings').length }
  ]

  // Filtered and sorted data
  const filteredData = useMemo(() => {
    let filtered = mockAuditLogs.filter(log => {
      const matchesSearch = searchTerm === '' || 
        log.adminUser.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.affectedEntity.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesActionType = selectedActionTypes.length === 0 || 
        selectedActionTypes.includes(log.actionType)
      
      const matchesEntityType = selectedEntityTypes.length === 0 || 
        selectedEntityTypes.includes(log.entityType)

      const matchesDateRange = (!dateRange.start || new Date(log.timestamp) >= new Date(dateRange.start)) &&
        (!dateRange.end || new Date(log.timestamp) <= new Date(dateRange.end + 'T23:59:59'))

      return matchesSearch && matchesActionType && matchesEntityType && matchesDateRange
    })

    // Sort data
    filtered.sort((a, b) => {
      const aValue = a[sortColumn]
      const bValue = b[sortColumn]
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [searchTerm, selectedActionTypes, selectedEntityTypes, dateRange, sortColumn, sortDirection])

  // Pagination
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredData.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredData, currentPage])

  const pagination: PaginationInfo = {
    currentPage,
    totalPages: Math.ceil(filteredData.length / itemsPerPage),
    totalItems: filteredData.length,
    itemsPerPage
  }

  // Table columns
  const columns: TableColumn<AuditLog>[] = [
    {
      key: 'timestamp',
      label: 'Timestamp',
      sortable: true,
      render: (value) => new Date(value).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    {
      key: 'actionType',
      label: 'Action Type',
      sortable: true,
      render: (value) => (
        <Badge variant="outline">{value}</Badge>
      )
    },
    {
      key: 'adminUser',
      label: 'Admin User',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'affectedEntity',
      label: 'Affected Entity',
      sortable: true,
      render: (value) => (
        <span className="text-muted-foreground">{value}</span>
      )
    },
    {
      key: 'entityType',
      label: 'Entity Type',
      sortable: true,
      render: (value) => (
        <Badge variant="secondary">{value}</Badge>
      )
    },
    {
      key: 'ipAddress',
      label: 'IP Address',
      sortable: true,
      render: (value) => (
        <code className="text-xs bg-muted px-1 py-0.5 rounded">{value}</code>
      )
    },
    {
      key: 'details',
      label: 'Details',
      render: (value) => (
        <span className="text-sm text-muted-foreground truncate max-w-xs block">
          {value}
        </span>
      )
    }
  ]

  // Table actions
  const actions = [
    {
      label: 'View Details',
      onClick: (log: AuditLog) => {
        setSelectedLog(log)
        setShowDetailsDialog(true)
      }
    }
  ]

  const handleExportLogs = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLoading(false)
    toast.success('Audit logs exported successfully')
  }

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedActionTypes([])
    setSelectedEntityTypes([])
    setDateRange({ start: '', end: '' })
    setCurrentPage(1)
  }

  const getActionTypeColor = (actionType: string) => {
    switch (actionType) {
      case 'User Created':
      case 'Workspace Created':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'User Disabled':
      case 'Workspace Archived':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'User Enabled':
      case 'Subscription Renewed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'Plan Changed':
      case 'Settings Updated':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Logs</h1>
          <p className="text-muted-foreground">
            Track all administrative actions and system changes
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowFilterDialog(true)}>
            <Filter className="mr-2 h-4 w-4" />
            Advanced Filters
          </Button>
          <Button onClick={handleExportLogs} disabled={loading}>
            <Download className="mr-2 h-4 w-4" />
            {loading ? 'Exporting...' : 'Export Logs'}
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Audit Trail ({filteredData.length} entries)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <SearchInput
              placeholder="Search by admin user, entity, or details..."
              value={searchTerm}
              onChange={setSearchTerm}
              className="flex-1"
            />
            <FilterDropdown
              label="Action Type"
              options={actionTypeOptions}
              selectedValues={selectedActionTypes}
              onSelectionChange={setSelectedActionTypes}
              className="w-full sm:w-auto"
            />
            <FilterDropdown
              label="Entity Type"
              options={entityTypeOptions}
              selectedValues={selectedEntityTypes}
              onSelectionChange={setSelectedEntityTypes}
              className="w-full sm:w-auto"
            />
          </div>

          {(selectedActionTypes.length > 0 || selectedEntityTypes.length > 0 || dateRange.start || dateRange.end) && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {selectedActionTypes.map(type => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {type}
                </Badge>
              ))}
              {selectedEntityTypes.map(type => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {type}
                </Badge>
              ))}
              {(dateRange.start || dateRange.end) && (
                <Badge variant="secondary" className="text-xs">
                  Date Range
                </Badge>
              )}
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear all
              </Button>
            </div>
          )}

          <DataTable
            data={paginatedData}
            columns={columns}
            loading={loading}
            pagination={pagination}
            onPageChange={setCurrentPage}
            onSort={(column, direction) => {
              setSortColumn(column)
              setSortDirection(direction)
            }}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            actions={actions}
            emptyState={{
              title: 'No audit logs found',
              description: 'No audit logs match your current search and filter criteria.',
              action: {
                label: 'Clear filters',
                onClick: clearFilters
              }
            }}
          />
        </CardContent>
      </Card>

      {/* Advanced Filters Dialog */}
      <FormDialog
        open={showFilterDialog}
        onOpenChange={setShowFilterDialog}
        title="Advanced Filters"
        description="Set date range and additional filter criteria"
        onSubmit={() => setShowFilterDialog(false)}
        submitText="Apply Filters"
        size="md"
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-date">End Date</Label>
              <Input
                id="end-date"
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>Quick Date Ranges</Label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const yesterday = new Date(today)
                  yesterday.setDate(yesterday.getDate() - 1)
                  setDateRange({
                    start: yesterday.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                  })
                }}
              >
                Last 24 hours
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const weekAgo = new Date(today)
                  weekAgo.setDate(weekAgo.getDate() - 7)
                  setDateRange({
                    start: weekAgo.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                  })
                }}
              >
                Last 7 days
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const monthAgo = new Date(today)
                  monthAgo.setMonth(monthAgo.getMonth() - 1)
                  setDateRange({
                    start: monthAgo.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                  })
                }}
              >
                Last 30 days
              </Button>
            </div>
          </div>
        </div>
      </FormDialog>

      {/* Log Details Dialog */}
      <FormDialog
        open={showDetailsDialog}
        onOpenChange={setShowDetailsDialog}
        title="Audit Log Details"
        description="Detailed information about this audit log entry"
        onSubmit={() => setShowDetailsDialog(false)}
        submitText="Close"
        size="lg"
      >
        {selectedLog && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Timestamp</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(selectedLog.timestamp).toLocaleString()}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Action Type</Label>
                <Badge className={getActionTypeColor(selectedLog.actionType)}>
                  {selectedLog.actionType}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Admin User</Label>
                <p className="text-sm text-muted-foreground">{selectedLog.adminUser}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">IP Address</Label>
                <code className="text-xs bg-muted px-2 py-1 rounded">{selectedLog.ipAddress}</code>
              </div>
              <div>
                <Label className="text-sm font-medium">Affected Entity</Label>
                <p className="text-sm text-muted-foreground">{selectedLog.affectedEntity}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Entity Type</Label>
                <Badge variant="secondary">{selectedLog.entityType}</Badge>
              </div>
            </div>
            
            <div>
              <Label className="text-sm font-medium">Details</Label>
              <p className="text-sm text-muted-foreground mt-1">{selectedLog.details}</p>
            </div>

            {selectedLog.metadata && Object.keys(selectedLog.metadata).length > 0 && (
              <div>
                <Label className="text-sm font-medium">Additional Metadata</Label>
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                    {JSON.stringify(selectedLog.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </FormDialog>
    </div>
  )
}
