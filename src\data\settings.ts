import { SystemSettings } from '../types/dashboard'

export const mockSystemSettings: SystemSettings = {
  emailNotifications: {
    sendActivationEmails: true,
    sendExpiryReminders: true,
    sendRenewalConfirmations: true,
    sendWeeklyDigest: false
  },
  general: {
    maintenanceMode: false,
    allowNewRegistrations: true,
    requireEmailVerification: true
  }
}

export const updateSystemSettings = (newSettings: Partial<SystemSettings>): SystemSettings => {
  return {
    ...mockSystemSettings,
    ...newSettings,
    emailNotifications: {
      ...mockSystemSettings.emailNotifications,
      ...(newSettings.emailNotifications || {})
    },
    general: {
      ...mockSystemSettings.general,
      ...(newSettings.general || {})
    }
  }
}
