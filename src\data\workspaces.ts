import { type Workspace } from '../types/dashboard'

export const mockWorkspaces: Workspace[] = [
  {
    id: 'ws-1',
    name: 'TechCorp Solutions',
    ownerName: '<PERSON>',
    ownerEmail: '<EMAIL>',
    planType: 'Enterprise',
    createdDate: '2023-03-10',
    memberCount: 25,
    isArchived: false,
    description: 'Leading technology solutions provider',
    settings: {
      allowInvites: true,
      requireApproval: true,
      maxMembers: 50
    }
  },
  {
    id: 'ws-2',
    name: 'Creative Design Studio',
    ownerName: '<PERSON>',
    ownerEmail: '<EMAIL>',
    planType: 'Premium',
    createdDate: '2023-08-15',
    memberCount: 8,
    isArchived: false,
    description: 'Creative design and branding agency',
    settings: {
      allowInvites: true,
      requireApproval: false,
      maxMembers: 15
    }
  },
  {
    id: 'ws-3',
    name: 'StartupInc',
    ownerName: '<PERSON>',
    ownerEmail: '<EMAIL>',
    planType: 'Basic',
    createdDate: '2024-01-20',
    memberCount: 3,
    isArchived: false,
    description: 'Innovative startup focused on AI solutions',
    settings: {
      allowInvites: false,
      requireApproval: true,
      maxMembers: 5
    }
  },
  {
    id: 'ws-4',
    name: 'Marketing Pro Agency',
    ownerName: 'Emily Rodriguez',
    ownerEmail: '<EMAIL>',
    planType: 'Premium',
    createdDate: '2023-11-05',
    memberCount: 12,
    isArchived: false,
    description: 'Full-service digital marketing agency',
    settings: {
      allowInvites: true,
      requireApproval: true,
      maxMembers: 15
    }
  },
  {
    id: 'ws-5',
    name: 'Wilson Consulting',
    ownerName: 'David Wilson',
    ownerEmail: '<EMAIL>',
    planType: 'Basic',
    createdDate: '2023-12-01',
    memberCount: 2,
    isArchived: true,
    description: 'Business strategy consulting firm',
    settings: {
      allowInvites: false,
      requireApproval: true,
      maxMembers: 5
    }
  },
  {
    id: 'ws-6',
    name: 'E-Commerce Solutions',
    ownerName: 'Lisa Thompson',
    ownerEmail: '<EMAIL>',
    planType: 'Enterprise',
    createdDate: '2023-05-12',
    memberCount: 18,
    isArchived: false,
    description: 'E-commerce platform development and consulting',
    settings: {
      allowInvites: true,
      requireApproval: false,
      maxMembers: 50
    }
  },
  {
    id: 'ws-7',
    name: 'Garcia & Associates',
    ownerName: 'Robert Garcia',
    ownerEmail: '<EMAIL>',
    planType: 'Premium',
    createdDate: '2023-09-20',
    memberCount: 6,
    isArchived: false,
    description: 'Corporate law firm specializing in business law',
    settings: {
      allowInvites: true,
      requireApproval: true,
      maxMembers: 15
    }
  },
  {
    id: 'ws-8',
    name: 'HealthTech Innovations',
    ownerName: 'Amanda Lee',
    ownerEmail: '<EMAIL>',
    planType: 'Premium',
    createdDate: '2023-07-08',
    memberCount: 14,
    isArchived: false,
    description: 'Healthcare technology solutions and medical devices',
    settings: {
      allowInvites: true,
      requireApproval: true,
      maxMembers: 15
    }
  },
  {
    id: 'ws-9',
    name: 'Brown Real Estate',
    ownerName: 'James Brown',
    ownerEmail: '<EMAIL>',
    planType: 'Basic',
    createdDate: '2024-02-14',
    memberCount: 4,
    isArchived: false,
    description: 'Residential and commercial real estate services',
    settings: {
      allowInvites: true,
      requireApproval: false,
      maxMembers: 5
    }
  },
  {
    id: 'ws-10',
    name: 'Davis Financial Services',
    ownerName: 'Jennifer Davis',
    ownerEmail: '<EMAIL>',
    planType: 'Enterprise',
    createdDate: '2023-04-18',
    memberCount: 22,
    isArchived: false,
    description: 'Comprehensive financial planning and investment services',
    settings: {
      allowInvites: true,
      requireApproval: true,
      maxMembers: 50
    }
  },
  {
    id: 'ws-11',
    name: 'Martinez Architecture',
    ownerName: 'Kevin Martinez',
    ownerEmail: '<EMAIL>',
    planType: 'Premium',
    createdDate: '2023-10-12',
    memberCount: 9,
    isArchived: false,
    description: 'Architectural design and construction planning',
    settings: {
      allowInvites: true,
      requireApproval: true,
      maxMembers: 15
    }
  },
  {
    id: 'ws-12',
    name: 'Green Fashion Brand',
    ownerName: 'Rachel Green',
    ownerEmail: '<EMAIL>',
    planType: 'Basic',
    createdDate: '2023-12-20',
    memberCount: 1,
    isArchived: true,
    description: 'Sustainable fashion and clothing brand',
    settings: {
      allowInvites: false,
      requireApproval: true,
      maxMembers: 5
    }
  }
]
