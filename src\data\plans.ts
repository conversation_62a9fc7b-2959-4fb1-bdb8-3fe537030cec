import { Plan } from '../types/dashboard'

export const mockPlans: Plan[] = [
  {
    id: 'plan-basic',
    name: 'Basic',
    monthlyPrice: 19.99,
    userLimit: 5,
    storageLimit: 10,
    apiCallsLimit: 1000,
    features: [
      'Up to 5 team members',
      '10GB storage',
      '1,000 API calls/month',
      'Basic support',
      'Standard templates',
      'Email integration'
    ],
    isActive: true,
    description: 'Perfect for small teams and startups getting started with AI-powered solutions.'
  },
  {
    id: 'plan-premium',
    name: 'Premium',
    monthlyPrice: 49.99,
    userLimit: 15,
    storageLimit: 50,
    apiCallsLimit: 5000,
    features: [
      'Up to 15 team members',
      '50GB storage',
      '5,000 API calls/month',
      'Priority support',
      'Advanced templates',
      'Email & Slack integration',
      'Custom workflows',
      'Analytics dashboard',
      'API access'
    ],
    isActive: true,
    isPopular: true,
    description: 'Ideal for growing businesses that need advanced features and integrations.'
  },
  {
    id: 'plan-enterprise',
    name: 'Enterprise',
    monthlyPrice: 99.99,
    userLimit: 50,
    storageLimit: 200,
    apiCallsLimit: 20000,
    features: [
      'Up to 50 team members',
      '200GB storage',
      '20,000 API calls/month',
      '24/7 dedicated support',
      'Custom templates',
      'All integrations',
      'Advanced workflows',
      'Advanced analytics',
      'Full API access',
      'SSO integration',
      'Custom branding',
      'Dedicated account manager'
    ],
    isActive: true,
    description: 'Comprehensive solution for large organizations with enterprise-grade requirements.'
  },
  {
    id: 'plan-legacy',
    name: 'Legacy Starter',
    monthlyPrice: 9.99,
    userLimit: 3,
    storageLimit: 5,
    apiCallsLimit: 500,
    features: [
      'Up to 3 team members',
      '5GB storage',
      '500 API calls/month',
      'Community support',
      'Basic templates'
    ],
    isActive: false,
    description: 'Legacy plan no longer available for new subscriptions.'
  }
]
